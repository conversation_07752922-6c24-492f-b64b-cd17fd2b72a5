using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Odrc.Dots.Entities.Common;

namespace Odrc.Dots.Areas.Transfers.Models.Mosci
{
    //public class MosciViewModel
    //{
    //    public DateTime SCHDATE { get; set; }
    //    public string SCHDINST { get; set; }
    //    public string INSTNO { get; set; }
    //    public string OID { get; set; }
    //    public int RECNO { get; set; }
    //    public string DESCRL { get; set; }
    //    public string MANT { get; set; }
    //    public string STATIONAME { get; set; }
    //    public DateTime SYSDATE { get; set; }
    //    public string ROWID { get; set; }
    //    public string LastName { get; set; }
    //    public string FirstName { get; set; }
    //    public string InmateIdPrefix { get; set; }
    //    public string OffenderId { get; set; }
    //    public List<SelectListItem> Prefix { get; set; }
    //}
        public class MosciViewModel
        {
            public DateTime SchDate { get; set; }
            public int? SchdInst { get; set; }
            public int? Instno { get; set; }
            public string Oid { get; set; } = "";
            public int Recno { get; set; }
            public string Descrl { get; set; } = "";
            public string Mant { get; set; } = "";
            public string Stationame { get; set; } = "";
            public DateTime SysDate { get; set; }
            public string Rowid { get; set; } = "";
            public string LastName { get; set; } = "";
            public string FirstName { get; set; } = "";
            public string InmateIdPrefix { get; set; } = "";
            public string OffenderId { get; set; } = "";
            public List<SelectListItem> Prefix { get; set; } = new List<SelectListItem>();

            // Combined property for display purposes
            public string CombinedOffenderId 
            { get;set;
            //get => string.IsNullOrEmpty(InmateIdPrefix) ? OffenderId : InmateIdPrefix + OffenderId;
            //set
            //{
            //    // When setting combined ID, extract prefix and offender ID
            //    if (!string.IsNullOrEmpty(value) && value.Length > 0)
            //    {
            //        if (char.IsLetter(value[0]))
            //        {
            //            InmateIdPrefix = value[0].ToString().ToUpper();
            //            OffenderId = value.Length > 1 ? value : "";
            //        }
            //        else
            //        {
            //            InmateIdPrefix = ""; // Don't set default prefix
            //            OffenderId = value;
            //        }
            //    }
            //    else
            //    {
            //        InmateIdPrefix = "";
            //        OffenderId = "";
            //    }
            //}
        }

            // Properties for row operations
            public bool IsSelected { get; set; }
            public bool IsMarkedForRemoval { get; set; }
            public bool IsMarkedForDeletion { get; set; }
        }

    public class MosciPageViewModel
    {
        public MosciPageViewModel()
        {
            Inmates = new List<MosciViewModel>();
            SearchPrefix = "A";
            SearchOffenderId = "";
           
            PrefixOptions = new List<SelectListItem>();
            SchdInstDrp = new List<SelectListItem>();
            InstnoDrp = new List<SelectListItem>();
        }

        // Search parameters
        [Display(Name = "Prefix")]
        public string SearchPrefix { get; set; } = "";

        [Display(Name = "Offender ID")]
        [StringLength(6, ErrorMessage = "Offender ID cannot exceed 6 characters")]
        public string SearchOffenderId { get; set; } = "";

        // List of inmates for the grid
        public List<MosciViewModel> Inmates { get; set; }

        // Dropdown options
        public List<SelectListItem> PrefixOptions { get; set; }
        public List<SelectListItem> SchdInstDrp { get; set; }
        public List<SelectListItem> InstnoDrp { get; set; }


        // UI state
        public string Message { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
        public bool HasSearchResults { get; set; }

        // Maximum number of rows allowed
        public const int MaxRows = 19;
    }

}