
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Odrc.Dots.Data.Common.Utilities;
using Odrc.Dots.Data.HelperMethods;
using Odrc.Dots.Data.Transfers.Interfaces;
using Odrc.Dots.Entities.Transfers.WorkLists;
using System;
using Odrc.Dots.Entities.Rh;
using Odrc.Dots.Entities.Common.Lookup;
using Odrc.Dots.Core;
using Odrc.Dots.Entities.Common;
using System.Configuration;
using Odrc.Dots.Entities.Transfers;

namespace Odrc.Dots.Data.Transfers.RepositoryClasses
{
    public class MosciRepository : IMosciRepository
    {
        

        public List<MosciData> GetMosciInfoByOaksId(string oaksId)
        {
            // Initialise the list that will be returned
            var mosciDataList = new List<MosciData>();

            using (var connection = new SqlConnection(ConfigurationManager.ConnectionStrings[GlobalConstants.ConnectionStringDots].ToString()))
            using (var command = new SqlCommand("[dbo].[sp1314_CMENU_GetMOSCHRecords]", connection))
            {
                command.CommandType = CommandType.StoredProcedure;

                // Allow null or empty OID
                oaksId = string.IsNullOrWhiteSpace(oaksId) ? null : oaksId;
                command.Parameters.AddWithValue("@OID",(object)oaksId ?? DBNull.Value);

                connection.Open();
                using (var reader = command.ExecuteReader())
                {
                    // Read all rows instead of just one
                    while (reader.Read())
                    {
                        var mosciData = new MosciData
                        {
                            Oid = reader["OID"].ToString().Trim(),
                            SchdInst = (int?)reader["SCHDINST"],
                            Instno = (int?)reader["INSTNO"],
                            Descrl = reader["COMM"].ToString().Trim(),
                            Rowid = reader["ROWID"].ToString().Trim(),
                        };

                        var schdate = reader["SCHDATE"];
                        if (schdate != DBNull.Value)
                        {
                            mosciData.SchDate = Convert.ToDateTime(schdate);
                        }

                        mosciDataList.Add(mosciData);
                    }
                }
            }

            return mosciDataList;
        }

        //public void InsertorUpdateMosci(string offenderNumber, string sourceOffenderNumber, string targetOffenderNumber, string userName, string screenName)
        public int InsertorUpdateMosci(string combineOffenderId, string SchDate, int Instno, int SchdInst, string Descrl, string username, string rowId)
        {
            int result = 0;
            using (var connection = new SqlConnection(Helper.ConnectionString))
            //using (var cmd = CreateCommand(CommandType.StoredProcedure, "Dots_Victims_spUpdateParol"))
            using (var cmd = new SqlCommand("[dbo].[sp1314_CMENU_Save_Move]", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                
                
                cmd.Parameters.Add("@OID", SqlDbType.VarChar, 10).Value = combineOffenderId;
                cmd.Parameters.Add("@SCHDATE", SqlDbType.VarChar, 10).Value = SchDate;
                cmd.Parameters.Add("@INSTNO", SqlDbType.Int).Value = Instno;
                cmd.Parameters.Add("@SCHDINST", SqlDbType.Int).Value = SchdInst;
                cmd.Parameters.Add("@COMM", SqlDbType.VarChar, 50).Value= Descrl;
                cmd.Parameters.Add("@USERID", SqlDbType.VarChar, 50).Value = username;
                if(rowId!=null)
                {
                    cmd.Parameters.Add("@ROWID", SqlDbType.VarChar, 50).Value = rowId;
                }
                
                cmd.Connection = connection;
                connection.Open();
                result = cmd.ExecuteNonQuery();
                connection.Close();
                
                                                                                                                              
            }
            return result;
        }

        public int DeleteMosciRecord(string rowId, string username)
        {
            int result = 0;
            using (var connection = new SqlConnection(Helper.ConnectionString))
            using (var cmd = new SqlCommand("[dbo].[sp1311_CHENU_DeleteMoveRecord]", connection))
            {
                cmd.CommandType = CommandType.StoredProcedure;

                cmd.Parameters.Add("@USERID", SqlDbType.VarChar, 50).Value = "A424964";

                if (rowId != null)
                {
                    cmd.Parameters.Add("@ROWID", SqlDbType.VarChar, 50).Value = "D527DC13-0234-493D-8F57-C6AE01C2C7D4";
                }

                cmd.Connection = connection;
                connection.Open();
                result = cmd.ExecuteNonQuery();
                connection.Close();
            }

            return result;
        }

    }
}