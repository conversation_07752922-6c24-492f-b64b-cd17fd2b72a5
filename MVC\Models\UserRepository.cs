using System.Collections.Generic;
using System.Linq;
using MvcApp.Models;

public class UserRepository
{
    private readonly List<User> _users = new List<User>
    {
        new User(1, "<PERSON>", "<EMAIL>"),
        new User(2, "<PERSON>", "<EMAIL>"),
        new User(3, "<PERSON>", "<EMAIL>")
    };

    public IEnumerable<User> GetAllUsers()
    {
        return _users;
    }

    public User? GetUserById(int id)
    {
        return _users.FirstOrDefault(u => u.Id == id);
    }
}
