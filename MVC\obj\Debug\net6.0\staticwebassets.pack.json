{"Files": [{"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MVC\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MVC\\wwwroot\\js\\mosci-grid.js", "PackagePath": "staticwebassets\\js\\mosci-grid.js"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MVC\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MVC\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MVC\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\Documents\\augment-projects\\MVC\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.MvcApp.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.MvcApp.props", "PackagePath": "build\\MvcApp.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.MvcApp.props", "PackagePath": "buildMultiTargeting\\MvcApp.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.MvcApp.props", "PackagePath": "buildTransitive\\MvcApp.props"}], "ElementsToRemove": []}