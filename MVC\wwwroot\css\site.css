/* Bootstrap 3.3.7 Customizations */
body {
  padding-top: 20px;
  padding-bottom: 60px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333;
  background-color: #fff;
}

/* Footer styles */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  padding-top: 20px;
}

/* Panel customizations */
.panel-primary > .panel-heading {
  background-color: #337ab7;
  border-color: #337ab7;
  color: white;
}

/* Table styles */
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > td {
  padding: 5px;
  vertical-align: middle;
}

.table-bordered {
  border: 1px solid #ddd;
}

/* Form control sizes for Bootstrap 3 */
.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

/* Button spacing */
.btn {
  margin-right: 5px;
}

/* MOSCI specific styles */
.no-print {
  margin-bottom: 20px;
}

#inmateTable th {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
}

#inmateTable input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .text-right {
    text-align: left;
    margin-top: 10px;
  }
}

/* Spinning icon for loading indicators */
.glyphicon-spin {
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Date picker styling */
.datepicker-trigger {
  background-color: #f5f5f5;
  border-left: 1px solid #ccc;
  padding: 5px 8px;
}

.datepicker-trigger:hover {
  background-color: #e8e8e8;
}

.datepicker-trigger .glyphicon-calendar {
  color: #337ab7;
  font-size: 12px;
}

/* Ensure input group sizing is consistent */
.input-group-sm .input-group-addon {
  padding: 5px 8px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

/* Custom datepicker styling to match the expected result */
.ui-datepicker {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  font-size: 11px;
  width: 200px;
  border: 1px solid #999;
  background: white;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
  padding: 0;
}

.ui-datepicker .ui-datepicker-header {
  background: #f0f0f0;
  border: none;
  border-bottom: 1px solid #ccc;
  color: #333;
  font-weight: normal;
  padding: 4px 8px;
  position: relative;
  height: 20px;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  position: absolute;
  top: 2px;
  width: 16px;
  height: 16px;
  background: #f0f0f0;
  border: 1px solid #999;
  cursor: pointer;
  text-align: center;
  line-height: 14px;
  font-size: 10px;
  color: #333;
}

.ui-datepicker .ui-datepicker-prev {
  left: 4px;
}

.ui-datepicker .ui-datepicker-next {
  right: 4px;
}

.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
  background: #e0e0e0;
}

.ui-datepicker .ui-datepicker-title {
  line-height: 20px;
  margin: 0 25px;
  text-align: center;
  font-weight: bold;
  font-size: 11px;
}

/* Style the month and year dropdowns */
.ui-datepicker .ui-datepicker-month,
.ui-datepicker .ui-datepicker-year {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  font-size: 11px;
  border: 1px solid #999;
  background: white;
  padding: 1px 2px;
  margin: 0 2px;
  color: #333;
}

.ui-datepicker .ui-datepicker-month {
  width: 70px;
}

.ui-datepicker .ui-datepicker-year {
  width: 50px;
}

.ui-datepicker table {
  width: 100%;
  font-size: 11px;
  border-collapse: collapse;
  margin: 0;
  border-spacing: 0;
}

.ui-datepicker th {
  padding: 4px 2px;
  text-align: center;
  font-weight: normal;
  border: none;
  background: #f8f8f8;
  color: #666;
  font-size: 10px;
  border-bottom: 1px solid #ddd;
}

.ui-datepicker td {
  border: none;
  padding: 0;
  text-align: center;
}

.ui-datepicker td span,
.ui-datepicker td a {
  display: block;
  padding: 2px;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  font-size: 11px;
  color: #333;
  width: 20px;
  height: 16px;
  line-height: 16px;
  margin: 1px;
}

.ui-datepicker td a:hover {
  background: #316AC5;
  color: white;
  border: 1px solid #316AC5;
}

.ui-datepicker .ui-datepicker-today a {
  background: #316AC5;
  color: white;
  font-weight: bold;
  border: 1px solid #316AC5;
}

.ui-datepicker .ui-datepicker-current-day a {
  background: #316AC5;
  color: white;
  font-weight: bold;
  border: 1px solid #316AC5;
}

.ui-datepicker .ui-datepicker-other-month a {
  color: #ccc;
}

/* Hide the default jQuery UI icons and use custom arrows */
.ui-datepicker .ui-icon {
  display: none;
}

.ui-datepicker .ui-datepicker-prev:before {
  content: "◀";
  font-size: 8px;
}

.ui-datepicker .ui-datepicker-next:before {
  content: "▶";
  font-size: 8px;
}
