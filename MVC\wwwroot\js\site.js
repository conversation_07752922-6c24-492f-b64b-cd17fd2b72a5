// MOSCI Application JavaScript Functions

// Global namespace for MOSCI application
var MOSCI = MOSCI || {};

// Initialize numeric validation for offender ID fields
MOSCI.initNumericValidation = function() {
    // Use event delegation for dynamically added elements
    $(document).on('keypress', '.onlyNumeric', function(e) {
        // Allow only numbers (0-9)
        if (e.which < 48 || e.which > 57) {
            e.preventDefault();
        }
    });
};

// Initialize when the document is ready
$(document).ready(function() {
    // Initialize numeric validation
    MOSCI.initNumericValidation();
});
